<script setup>
import { ref, computed, onMounted } from 'vue'
import CanvasMap from './components/CanvasMap.vue'

// 网格大小和画布尺寸
const gridSize = ref(40)
const canvasWidth = 800
const canvasHeight = 600

// 计算网格行列数
const rows = computed(() => Math.floor(canvasHeight / gridSize.value))
const cols = computed(() => Math.floor(canvasWidth / gridSize.value))

// 示例点数据（使用网格坐标）
const points = ref([
  { x: 2, y: 2, type: 'start' },
  { x: 5, y: 3, type: 'normal' },
  { x: 8, y: 5, type: 'normal' },
  { x: 10, y: 6, type: 'normal' },
  { x: 8, y: 8, type: 'obstacle' },
  { x: 6, y: 10, type: 'obstacle' },
  { x: 12, y: 10, type: 'end' }
])

// 路径点数据（使用网格坐标，确保只沿着网格线移动）
const pathPoints = ref([
  { x: 2, y: 2 },  // 起点
  { x: 2, y: 5 },  // 垂直向下
  { x: 5, y: 5 },  // 水平向右
  { x: 5, y: 8 },  // 垂直向下
  { x: 9, y: 8 },  // 水平向右
  { x: 9, y: 10 }, // 垂直向下
  { x: 12, y: 10 } // 水平向右到终点
])

// 当前选中的点
const selectedPoint = ref(null)

// 气泡提示相关
const showBubble = ref(false)
const bubbleMessage = ref('')
const bubbleTimeout = ref(null)

// 显示气泡提示
const showBubbleMessage = (message) => {
  bubbleMessage.value = message
  showBubble.value = true
  
  // 清除之前的定时器
  if (bubbleTimeout.value) {
    clearTimeout(bubbleTimeout.value)
  }
  
  // 设置新的定时器，3秒后自动隐藏气泡
  bubbleTimeout.value = setTimeout(() => {
    showBubble.value = false
  }, 3000)
}

// 处理点击点事件
const handlePointClick = (point) => {
  selectedPoint.value = point
  showBubbleMessage(`已选择${getPointTypeName(point.type)}`)
}

// 关闭详情面板
const closeDetails = () => {
  selectedPoint.value = null
}

// 添加新的点
const addPoint = (type) => {
  // 随机选择网格坐标
  const x = Math.floor(Math.random() * cols.value)
  const y = Math.floor(Math.random() * rows.value)
  points.value.push({ x, y, type })
  showBubbleMessage(`已添加${getPointTypeName(type)}`)
}

// 生成一条沿着网格线的路径
const generateGridPath = () => {
  // 找到起点和终点
  const start = points.value.find(p => p.type === 'start')
  const end = points.value.find(p => p.type === 'end')
  
  if (!start || !end) {
    alert('请先添加起点和终点')
    return
  }
  
  // 清空当前路径
  pathPoints.value = []
  
  // 添加起点
  pathPoints.value.push({ x: start.x, y: start.y })
  
  // 简单的路径生成（先水平移动一段，再垂直移动到终点）
  const midX = Math.floor((start.x + end.x) / 2)
  
  // 添加中间点
  pathPoints.value.push({ x: midX, y: start.y })
  pathPoints.value.push({ x: midX, y: end.y })
  
  // 添加终点
  pathPoints.value.push({ x: end.x, y: end.y })
  
  showBubbleMessage('路径已生成')
}

// 清除路径
const clearPath = () => {
  pathPoints.value = []
  showBubbleMessage('路径已清除')
}

// 调整网格大小
const changeGridSize = (size) => {
  gridSize.value = size
  showBubbleMessage(`网格大小已调整为 ${size}px`)
}

// 添加起点和终点
const addStartPoint = () => {
  // 移除现有的起点
  points.value = points.value.filter(p => p.type !== 'start')
  // 添加新起点
  const x = Math.floor(Math.random() * (cols.value / 3))
  const y = Math.floor(Math.random() * rows.value)
  points.value.push({ x, y, type: 'start' })
  showBubbleMessage('已添加起点')
}

const addEndPoint = () => {
  // 移除现有的终点
  points.value = points.value.filter(p => p.type !== 'end')
  // 添加新终点
  const x = Math.floor(cols.value / 3 * 2 + Math.random() * (cols.value / 3))
  const y = Math.floor(Math.random() * rows.value)
  points.value.push({ x, y, type: 'end' })
  showBubbleMessage('已添加终点')
}

// 获取点类型的中文名称
const getPointTypeName = (type) => {
  switch(type) {
    case 'start': return '起点'
    case 'end': return '终点'
    case 'obstacle': return '障碍物'
    default: return '普通点'
  }
}

onMounted(() => {
  // 确保有起点和终点
  if (!points.value.some(p => p.type === 'start')) {
    addStartPoint()
  }
  if (!points.value.some(p => p.type === 'end')) {
    addEndPoint()
  }
})
</script>

<template>
  <div class="app-container">
    <h1>网格地图绘制</h1>
    
    <div class="controls">
      <button @click="addStartPoint">添加起点</button>
      <button @click="addEndPoint">添加终点</button>
      <button @click="addPoint('normal')">添加普通点</button>
      <button @click="addPoint('obstacle')">添加障碍点</button>
      <button @click="generateGridPath">生成路径</button>
      <button @click="clearPath">清除路径</button>
      
      <div class="grid-control">
        <span>网格大小:</span>
        <button @click="changeGridSize(20)">小</button>
        <button @click="changeGridSize(40)">中</button>
        <button @click="changeGridSize(60)">大</button>
      </div>
      
      <!-- 气泡提示 -->
      <Transition name="bubble">
        <div v-if="showBubble" class="bubble-message">
          {{ bubbleMessage }}
        </div>
      </Transition>
    </div>
    
    <CanvasMap 
      :points="points" 
      :pathPoints="pathPoints" 
      :width="canvasWidth" 
      :height="canvasHeight"
      :gridSize="gridSize"
      @point-click="handlePointClick"
    />
    
    <!-- 点详情面板 -->
    <div v-if="selectedPoint" class="point-details">
      <div class="details-header">
        <h3>点详情</h3>
        <button class="close-btn" @click="closeDetails">×</button>
      </div>
      <div class="details-content">
        <p><strong>类型:</strong> {{ getPointTypeName(selectedPoint.type) }}</p>
        <p><strong>网格坐标:</strong> ({{ selectedPoint.x }}, {{ selectedPoint.y }})</p>
        <p><strong>像素坐标:</strong> ({{ selectedPoint.x * gridSize }}, {{ selectedPoint.y * gridSize }})</p>
      </div>
    </div>
    
    <div class="info">
      <p>网格大小: {{ gridSize }}px ({{ cols }} × {{ rows }})</p>
      <p>点的总数: {{ points.length }}</p>
      <p>路径点数: {{ pathPoints.length }}</p>
      <p>提示: 点击地图上的点可查看详情</p>
    </div>
  </div>
</template>

<style>
.app-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.controls {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  position: relative;
}

.controls button {
  margin: 0 5px;
}

.grid-control {
  margin-left: 20px;
  display: flex;
  align-items: center;
}

.grid-control span {
  margin-right: 10px;
}

.info {
  margin-top: 20px;
  text-align: left;
  background-color: #333;
  padding: 15px;
  border-radius: 8px;
}

.point-details {
  position: relative;
  margin: 20px auto;
  max-width: 400px;
  background-color: #222;
  border: 1px solid #444;
  border-radius: 8px;
  padding: 15px;
  text-align: left;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  border-bottom: 1px solid #444;
  padding-bottom: 10px;
}

.details-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #aaa;
  padding: 0;
}

.close-btn:hover {
  color: white;
}

.details-content p {
  margin: 8px 0;
}

/* 气泡提示样式 */
.bubble-message {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  z-index: 100;
}

/* 气泡动画 */
.bubble-enter-active {
  animation: bubble-in 0.5s;
}

.bubble-leave-active {
  animation: bubble-in 0.5s reverse;
}

@keyframes bubble-in {
  0% {
    transform: translateX(-50%) translateY(20px);
    opacity: 0;
  }
  50% {
    transform: translateX(-50%) translateY(-5px);
    opacity: 1;
  }
  100% {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}
</style>

