<script setup>
import { ref, onMounted, watch, computed, onUnmounted } from 'vue'

const props = defineProps({
  gridSize: {
    type: Number,
    default: 40 // 网格大小，单位像素
  },
  points: {
    type: Array,
    default: () => []
  },
  pathPoints: {
    type: Array,
    default: () => []
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  }
})

const emit = defineEmits(['pointClick'])

const canvasRef = ref(null)
const animationRef = ref(null)
const animationPhase = ref(0)

// 气泡提示相关
const showBubble = ref(false)
const bubblePoint = ref(null)
const bubbleOpacity = ref(0)
const bubbleScale = ref(0)
const bubbleTimeout = ref(null)

// 路径动画相关
const pathAnimationActive = ref(false)
const pathAnimationProgress = ref(0)
const pathAnimationStartTime = ref(null)
const pathAnimationDuration = ref(5000) // 完成一次路径动画的总时间（毫秒）

// 计算网格行列数
const rows = computed(() => Math.floor(props.height / props.gridSize))
const cols = computed(() => Math.floor(props.width / props.gridSize))

// 计算路径总长度（用于动画）
const pathTotalLength = computed(() => {
  if (props.pathPoints.length < 2) return 0
  
  let length = 0
  for (let i = 0; i < props.pathPoints.length - 1; i++) {
    const current = props.pathPoints[i]
    const next = props.pathPoints[i + 1]
    
    // 计算两点之间的曼哈顿距离（水平+垂直）
    length += Math.abs(next.x - current.x) + Math.abs(next.y - current.y)
  }
  
  return length * props.gridSize // 转换为像素长度
})

// 速度曲线函数 - 可以根据需要修改
const speedCurve = (t) => {
  // 这是一个示例速度曲线，呈现先加速后减速的效果
  // t 是归一化的时间 (0-1)
  // 返回值是归一化的速度 (0-1)
  
  // 使用正弦函数创建一个平滑的加速-减速曲线
  return 0.5 - 0.5 * Math.cos(Math.PI * t)
  
  // 其他可能的速度曲线:
  // 匀速: return 1
  // 加速: return t * t
  // 减速: return 1 - (1 - t) * (1 - t)
  // 先快后慢: return Math.sin(t * Math.PI / 2)
  // 先慢后快: return 1 - Math.cos(t * Math.PI / 2)
}

// 根据速度曲线计算当前进度
const calculateProgress = (elapsedTime) => {
  // 归一化时间 (0-1)
  const normalizedTime = (elapsedTime % pathAnimationDuration.value) / pathAnimationDuration.value
  
  // 应用速度曲线函数
  let progress = 0
  const steps = 100 // 积分步数
  const stepSize = normalizedTime / steps
  
  // 数值积分计算位移
  for (let i = 0; i < steps; i++) {
    const t = i * stepSize
    progress += speedCurve(t) * stepSize
  }
  
  // 确保进度在 0-1 范围内
  return Math.min(1, progress / speedCurve(0.5))
}

// 绘制地图
const drawMap = () => {
  const canvas = canvasRef.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  
  // 设置黑色背景
  ctx.fillStyle = 'black'
  ctx.fillRect(0, 0, props.width, props.height)
  
  // 绘制网格
  drawGrid(ctx)
  
  // 绘制路径线（带呼吸效果）
  drawPathWithBreathingEffect(ctx)
  
  // 绘制路径上的移动物体
  if (pathAnimationActive.value && props.pathPoints.length >= 2) {
    drawPathAnimation(ctx)
  }
  
  // 绘制点
  props.points.forEach(point => {
    ctx.beginPath()
    
    // 根据点的类型设置不同的颜色
    switch(point.type) {
      case 'start':
        ctx.fillStyle = 'green'
        break
      case 'end':
        ctx.fillStyle = 'red'
        break
      case 'obstacle':
        ctx.fillStyle = 'gray'
        break
      default:
        ctx.fillStyle = 'white'
    }
    
    // 将网格坐标转换为像素坐标
    const pixelX = point.x * props.gridSize
    const pixelY = point.y * props.gridSize
    
    ctx.arc(pixelX, pixelY, 8, 0, Math.PI * 2)
    ctx.fill()
  })
  
  // 绘制气泡
  if (showBubble.value && bubblePoint.value) {
    drawBubble(ctx, bubblePoint.value)
  }
  
  // 绘制速度曲线图表（用于调试）
  if (pathAnimationActive.value) {
    drawSpeedCurveGraph(ctx)
  }
}

// 绘制网格
const drawGrid = (ctx) => {
  ctx.strokeStyle = '#333' // 暗灰色网格线
  ctx.lineWidth = 1
  
  // 绘制垂直线
  for (let x = 0; x <= cols.value; x++) {
    ctx.beginPath()
    ctx.moveTo(x * props.gridSize, 0)
    ctx.lineTo(x * props.gridSize, props.height)
    ctx.stroke()
  }
  
  // 绘制水平线
  for (let y = 0; y <= rows.value; y++) {
    ctx.beginPath()
    ctx.moveTo(0, y * props.gridSize)
    ctx.lineTo(props.width, y * props.gridSize)
    ctx.stroke()
  }
}

// 绘制带呼吸效果的路径
const drawPathWithBreathingEffect = (ctx) => {
  if (props.pathPoints.length < 2) return
  
  // 计算呼吸效果的线宽
  const baseWidth = 3
  const pulseWidth = 2
  const lineWidth = baseWidth + Math.sin(animationPhase.value) * pulseWidth
  
  ctx.lineWidth = lineWidth
  ctx.strokeStyle = 'yellow'
  
  // 绘制路径（只沿着网格线移动）
  for (let i = 0; i < props.pathPoints.length - 1; i++) {
    const current = props.pathPoints[i]
    const next = props.pathPoints[i + 1]
    
    // 将网格坐标转换为像素坐标
    const startX = current.x * props.gridSize
    const startY = current.y * props.gridSize
    const endX = next.x * props.gridSize
    const endY = next.y * props.gridSize
    
    ctx.beginPath()
    ctx.moveTo(startX, startY)
    
    // 确保路径只沿着网格线移动（先水平后垂直）
    if (startX !== endX || startY !== endY) {
      ctx.lineTo(endX, startY) // 先水平移动
      ctx.lineTo(endX, endY)   // 再垂直移动
    } else {
      ctx.lineTo(endX, endY)   // 直接连接
    }
    
    ctx.stroke()
  }
}

// 绘制路径动画
const drawPathAnimation = (ctx) => {
  // 根据当前进度计算位置
  const position = getPositionOnPath(pathAnimationProgress.value)
  if (!position) return
  
  const { x, y } = position
  
  // 绘制移动物体（一个小球）
  ctx.beginPath()
  ctx.fillStyle = '#00ffff' // 青色
  ctx.shadowColor = '#00ffff'
  ctx.shadowBlur = 15
  ctx.arc(x, y, 6, 0, Math.PI * 2)
  ctx.fill()
  
  // 绘制尾迹效果
  const tailLength = 5 // 尾迹长度
  const tailStep = 0.02 // 尾迹间隔
  
  for (let i = 1; i <= tailLength; i++) {
    const tailProgress = Math.max(0, pathAnimationProgress.value - i * tailStep)
    const tailPos = getPositionOnPath(tailProgress)
    if (tailPos) {
      const opacity = 1 - i / tailLength
      const size = 6 - i * 0.8
      
      ctx.beginPath()
      ctx.fillStyle = `rgba(0, 255, 255, ${opacity})`
      ctx.shadowColor = `rgba(0, 255, 255, ${opacity})`
      ctx.shadowBlur = 10 * opacity
      ctx.arc(tailPos.x, tailPos.y, size, 0, Math.PI * 2)
      ctx.fill()
    }
  }
  
  // 重置阴影效果
  ctx.shadowBlur = 0
}

// 绘制速度曲线图表（用于调试）
const drawSpeedCurveGraph = (ctx) => {
  const graphWidth = 150
  const graphHeight = 80
  const graphX = props.width - graphWidth - 20
  const graphY = 20
  
  // 绘制背景
  ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
  ctx.fillRect(graphX, graphY, graphWidth, graphHeight)
  
  // 绘制边框
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)'
  ctx.lineWidth = 1
  ctx.strokeRect(graphX, graphY, graphWidth, graphHeight)
  
  // 绘制标题
  ctx.fillStyle = 'white'
  ctx.font = '12px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('速度曲线', graphX + graphWidth / 2, graphY - 5)
  
  // 绘制速度曲线
  ctx.beginPath()
  ctx.strokeStyle = '#00ffff'
  ctx.lineWidth = 2
  
  for (let i = 0; i <= 100; i++) {
    const t = i / 100
    const speed = speedCurve(t)
    const x = graphX + t * graphWidth
    const y = graphY + graphHeight - speed * graphHeight
    
    if (i === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  }
  
  ctx.stroke()
  
  // 绘制当前位置指示器
  const normalizedTime = ((performance.now() - pathAnimationStartTime.value) % pathAnimationDuration.value) / pathAnimationDuration.value
  const indicatorX = graphX + normalizedTime * graphWidth
  
  ctx.beginPath()
  ctx.strokeStyle = 'red'
  ctx.lineWidth = 2
  ctx.moveTo(indicatorX, graphY)
  ctx.lineTo(indicatorX, graphY + graphHeight)
  ctx.stroke()
  
  // 显示当前速度值
  const currentSpeed = speedCurve(normalizedTime)
  ctx.fillStyle = 'white'
  ctx.textAlign = 'left'
  ctx.fillText(`速度: ${currentSpeed.toFixed(2)}`, graphX + 5, graphY + 15)
  ctx.fillText(`进度: ${pathAnimationProgress.value.toFixed(2)}`, graphX + 5, graphY + 30)
}

// 根据路径进度获取位置
const getPositionOnPath = (progress) => {
  if (props.pathPoints.length < 2 || progress <= 0) {
    // 起点
    const start = props.pathPoints[0]
    return { 
      x: start.x * props.gridSize, 
      y: start.y * props.gridSize 
    }
  }
  
  if (progress >= 1) {
    // 终点
    const end = props.pathPoints[props.pathPoints.length - 1]
    return { 
      x: end.x * props.gridSize, 
      y: end.y * props.gridSize 
    }
  }
  
  // 计算当前所在的路径段
  let accumulatedLength = 0
  let totalLength = pathTotalLength.value / props.gridSize // 转换回网格单位
  
  for (let i = 0; i < props.pathPoints.length - 1; i++) {
    const current = props.pathPoints[i]
    const next = props.pathPoints[i + 1]
    
    // 计算当前段的曼哈顿距离
    const segmentLength = Math.abs(next.x - current.x) + Math.abs(next.y - current.y)
    const segmentProgress = segmentLength / totalLength
    
    // 检查是否在当前段内
    if (progress <= (accumulatedLength + segmentProgress)) {
      // 计算在当前段内的相对进度
      const segmentRelativeProgress = (progress - accumulatedLength) / segmentProgress
      
      // 计算当前位置（考虑先水平后垂直移动）
      const dx = next.x - current.x
      const dy = next.y - current.y
      
      let x, y
      
      if (dx !== 0 && dy !== 0) {
        // 如果既有水平又有垂直移动
        if (segmentRelativeProgress <= 0.5) {
          // 先水平移动
          const horizontalProgress = segmentRelativeProgress * 2
          x = current.x + dx * horizontalProgress
          y = current.y
        } else {
          // 再垂直移动
          const verticalProgress = (segmentRelativeProgress - 0.5) * 2
          x = next.x
          y = current.y + dy * verticalProgress
        }
      } else if (dx !== 0) {
        // 只有水平移动
        x = current.x + dx * segmentRelativeProgress
        y = current.y
      } else {
        // 只有垂直移动
        x = current.x
        y = current.y + dy * segmentRelativeProgress
      }
      
      // 转换为像素坐标
      return {
        x: x * props.gridSize,
        y: y * props.gridSize
      }
    }
    
    accumulatedLength += segmentProgress
  }
  
  // 如果没有找到匹配的段，返回终点
  const end = props.pathPoints[props.pathPoints.length - 1]
  return { 
    x: end.x * props.gridSize, 
    y: end.y * props.gridSize 
  }
}

// 开始路径动画
const startPathAnimation = () => {
  pathAnimationActive.value = true
  pathAnimationProgress.value = 0
  pathAnimationStartTime.value = performance.now()
}

// 停止路径动画
const stopPathAnimation = () => {
  pathAnimationActive.value = false
}

// 更新路径动画
const updatePathAnimation = (timestamp) => {
  if (!pathAnimationActive.value) return
  
  // 计算经过的时间（毫秒）
  const elapsedTime = timestamp - pathAnimationStartTime.value
  
  // 根据速度曲线计算当前进度
  pathAnimationProgress.value = calculateProgress(elapsedTime)
}

// 绘制气泡
const drawBubble = (ctx, point) => {
  const pixelX = point.x * props.gridSize
  const pixelY = point.y * props.gridSize
  
  const bubbleText = getPointTypeName(point.type)
  const bubbleWidth = ctx.measureText(bubbleText).width + 30
  const bubbleHeight = 30
  const bubbleX = pixelX + 20
  const bubbleY = pixelY - 20
  const radius = 10
  
  // 应用动画效果
  ctx.globalAlpha = bubbleOpacity.value
  ctx.save()
  ctx.translate(bubbleX + bubbleWidth / 2, bubbleY + bubbleHeight / 2)
  ctx.scale(bubbleScale.value, bubbleScale.value)
  ctx.translate(-(bubbleX + bubbleWidth / 2), -(bubbleY + bubbleHeight / 2))
  
  // 绘制气泡背景
  ctx.beginPath()
  ctx.moveTo(bubbleX + radius, bubbleY)
  ctx.lineTo(bubbleX + bubbleWidth - radius, bubbleY)
  ctx.quadraticCurveTo(bubbleX + bubbleWidth, bubbleY, bubbleX + bubbleWidth, bubbleY + radius)
  ctx.lineTo(bubbleX + bubbleWidth, bubbleY + bubbleHeight - radius)
  ctx.quadraticCurveTo(bubbleX + bubbleWidth, bubbleY + bubbleHeight, bubbleX + bubbleWidth - radius, bubbleY + bubbleHeight)
  ctx.lineTo(bubbleX + radius, bubbleY + bubbleHeight)
  ctx.quadraticCurveTo(bubbleX, bubbleY + bubbleHeight, bubbleX, bubbleY + bubbleHeight - radius)
  ctx.lineTo(bubbleX, bubbleY + radius)
  ctx.quadraticCurveTo(bubbleX, bubbleY, bubbleX + radius, bubbleY)
  ctx.closePath()
  
  // 添加气泡尖角
  ctx.moveTo(pixelX + 10, pixelY)
  ctx.lineTo(bubbleX + 5, bubbleY + bubbleHeight - 5)
  ctx.lineTo(bubbleX + 15, bubbleY + bubbleHeight)
  
  // 填充气泡
  ctx.fillStyle = 'rgba(60, 120, 255, 0.9)'
  ctx.fill()
  
  // 添加气泡边框
  ctx.strokeStyle = 'rgba(120, 180, 255, 0.8)'
  ctx.lineWidth = 2
  ctx.stroke()
  
  // 绘制文字
  ctx.fillStyle = 'white'
  ctx.font = 'bold 14px Arial'
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  ctx.fillText(bubbleText, bubbleX + bubbleWidth / 2, bubbleY + bubbleHeight / 2)
  
  ctx.restore()
  ctx.globalAlpha = 1.0
}

// 显示点气泡
const showBubbleForPoint = (point) => {
  bubblePoint.value = point
  showBubble.value = true
  
  // 清除之前的定时器
  if (bubbleTimeout.value) {
    clearTimeout(bubbleTimeout.value)
  }
  
  // 动画效果
  bubbleOpacity.value = 0
  bubbleScale.value = 0.3
  
  // 使用requestAnimationFrame实现平滑动画
  let startTime = null
  const duration = 300 // 动画持续时间（毫秒）
  
  const animateBubble = (timestamp) => {
    if (!startTime) startTime = timestamp
    const elapsed = timestamp - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用缓动函数使动画更自然
    const easeOutBack = (t) => {
      const c1 = 1.70158
      const c3 = c1 + 1
      return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2)
    }
    
    bubbleOpacity.value = progress
    bubbleScale.value = 0.3 + 0.7 * easeOutBack(progress)
    
    if (progress < 1) {
      requestAnimationFrame(animateBubble)
    }
  }
  
  requestAnimationFrame(animateBubble)
  
  // 设置新的定时器，3秒后自动隐藏气泡
  bubbleTimeout.value = setTimeout(() => {
    // 淡出动画
    let startFadeTime = null
    const fadeDuration = 200
    
    const fadeOutBubble = (timestamp) => {
      if (!startFadeTime) startFadeTime = timestamp
      const elapsed = timestamp - startFadeTime
      const progress = Math.min(elapsed / fadeDuration, 1)
      
      bubbleOpacity.value = 1 - progress
      bubbleScale.value = 1 - 0.5 * progress
      
      if (progress < 1) {
        requestAnimationFrame(fadeOutBubble)
      } else {
        showBubble.value = false
      }
    }
    
    requestAnimationFrame(fadeOutBubble)
  }, 3000)
}

// 处理画布点击事件
const handleCanvasClick = (event) => {
  const canvas = canvasRef.value
  if (!canvas) return
  
  // 获取点击位置相对于画布的坐标
  const rect = canvas.getBoundingClientRect()
  const x = event.clientX - rect.left
  const y = event.clientY - rect.top
  
  // 转换为网格坐标
  const gridX = Math.round(x / props.gridSize)
  const gridY = Math.round(y / props.gridSize)
  
  // 查找最近的点
  let closestPoint = null
  let minDistance = Infinity
  
  props.points.forEach(point => {
    const pointX = point.x
    const pointY = point.y
    const distance = Math.sqrt(Math.pow(gridX - pointX, 2) + Math.pow(gridY - pointY, 2))
    
    // 如果距离小于1个网格单位，且比之前找到的点更近
    if (distance < 1 && distance < minDistance) {
      closestPoint = point
      minDistance = distance
    }
  })
  
  // 如果找到了点，触发事件并显示气泡
  if (closestPoint) {
    emit('pointClick', closestPoint)
    showBubbleForPoint(closestPoint)
  } else if (props.pathPoints.length >= 2) {
    // 如果没有点击到点，但有路径，则切换动画状态
    if (pathAnimationActive.value) {
      stopPathAnimation()
    } else {
      startPathAnimation()
    }
  }
}

// 获取点类型的中文名称
const getPointTypeName = (type) => {
  switch(type) {
    case 'start': return '起点'
    case 'end': return '终点'
    case 'obstacle': return '障碍物'
    default: return '普通点'
  }
}

// 动画循环
const animate = (timestamp) => {
  animationPhase.value += 0.05
  
  // 更新路径动画
  updatePathAnimation(timestamp)
  
  drawMap()
  animationRef.value = requestAnimationFrame(animate)
}

// 监听点和路径的变化，重新绘制
watch(() => props.points, drawMap, { deep: true })
watch(() => props.pathPoints, (newPathPoints) => {
  drawMap()
  // 如果有新的路径点，自动开始动画
  if (newPathPoints.length >= 2) {
    startPathAnimation()
  }
}, { deep: true })
watch(() => props.gridSize, drawMap)

// 暴露方法给父组件
defineExpose({
  startPathAnimation,
  stopPathAnimation
})

onMounted(() => {
  drawMap()
  animate() // 启动动画
  
  // 如果已经有路径点，自动开始动画
  if (props.pathPoints.length >= 2) {
    startPathAnimation()
  }
})

// 组件卸载时清除动画
onUnmounted(() => {
  if (animationRef.value) {
    cancelAnimationFrame(animationRef.value)
  }
  if (bubbleTimeout.value) {
    clearTimeout(bubbleTimeout.value)
  }
})
</script>

<template>
  <div class="canvas-container">
    <canvas 
      ref="canvasRef" 
      :width="width" 
      :height="height"
      @click="handleCanvasClick"
    ></canvas>
    
    <div class="speed-controls" v-if="pathAnimationActive">
      <label>
        动画周期:
        <input 
          type="range" 
          min="1000" 
          max="10000" 
          step="500" 
          v-model.number="pathAnimationDuration" 
        />
        {{ pathAnimationDuration }}ms
      </label>
    </div>
  </div>
</template>

<style scoped>
.canvas-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
  position: relative;
}

canvas {
  cursor: pointer;
}

.speed-controls {
  margin-top: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 10px;
  border-radius: 5px;
  color: white;
}

.speed-controls label {
  display: flex;
  align-items: center;
  gap: 10px;
}

.speed-controls input {
  width: 200px;
}
</style>



