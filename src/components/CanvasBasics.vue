<template>
  <div class="canvas-basics">
    <h2>Canvas 基础绘制示例</h2>
    <p>五个正方块展示：点、线、图形、文字、呼吸灯动画</p>
    
    <div class="canvas-grid">
      <!-- 点的绘制 -->
      <div class="canvas-item">
        <h3>点的绘制</h3>
        <canvas ref="pointCanvas" width="200" height="200"></canvas>
        <div class="controls">
          <button @click="drawPoints">重新绘制点</button>
          <button @click="clearCanvas('point')">清空</button>
        </div>
      </div>
      
      <!-- 线的绘制 -->
      <div class="canvas-item">
        <h3>线的绘制</h3>
        <canvas ref="lineCanvas" width="200" height="200"></canvas>
        <div class="controls">
          <button @click="drawLines">重新绘制线</button>
          <button @click="clearCanvas('line')">清空</button>
        </div>
      </div>
      
      <!-- 图形的绘制 -->
      <div class="canvas-item">
        <h3>图形的绘制</h3>
        <canvas ref="shapeCanvas" width="200" height="200"></canvas>
        <div class="controls">
          <button @click="drawShapes">重新绘制图形</button>
          <button @click="clearCanvas('shape')">清空</button>
        </div>
      </div>
      
      <!-- 文字的绘制 -->
      <div class="canvas-item">
        <h3>文字的绘制</h3>
        <canvas ref="textCanvas" width="200" height="200"></canvas>
        <div class="controls">
          <button @click="drawTexts">重新绘制文字</button>
          <button @click="clearCanvas('text')">清空</button>
        </div>
      </div>

      <!-- 呼吸灯动画 -->
      <div class="canvas-item breathing-lights">
        <h3>呼吸灯动画</h3>
        <canvas ref="breathingCanvas" width="200" height="200"></canvas>
        <div class="controls">
          <button @click="startBreathingAnimation">开始呼吸灯</button>
          <button @click="stopBreathingAnimation">停止动画</button>
          <button @click="clearCanvas('breathing')">清空</button>
        </div>
      </div>
    </div>

    <!-- 综合示例 -->
    <div class="combined-example">
      <h3>综合示例</h3>
      <canvas ref="combinedCanvas" width="600" height="300"></canvas>
      <div class="controls">
        <button @click="drawCombined">绘制综合示例</button>
        <button @click="clearCanvas('combined')">清空</button>
        <button @click="animateCombined">动画效果</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Canvas引用
const pointCanvas = ref(null)
const lineCanvas = ref(null)
const shapeCanvas = ref(null)
const textCanvas = ref(null)
const breathingCanvas = ref(null)
const combinedCanvas = ref(null)

// 动画相关
const animationId = ref(null)
const animationTime = ref(0)
const breathingAnimationId = ref(null)
const breathingTime = ref(0)
const isBreathingActive = ref(false)

// 清空画布
const clearCanvas = (type) => {
  let canvas
  switch(type) {
    case 'point': canvas = pointCanvas.value; break
    case 'line': canvas = lineCanvas.value; break
    case 'shape': canvas = shapeCanvas.value; break
    case 'text': canvas = textCanvas.value; break
    case 'breathing': canvas = breathingCanvas.value; break
    case 'combined': canvas = combinedCanvas.value; break
  }
  
  if (canvas) {
    const ctx = canvas.getContext('2d')
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    // 设置黑色背景
    ctx.fillStyle = '#000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
  }
}

// 绘制点
const drawPoints = () => {
  const canvas = pointCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  clearCanvas('point')
  
  // 绘制不同类型的点
  const points = [
    { x: 50, y: 50, radius: 5, color: '#ff0000', type: '实心圆点' },
    { x: 150, y: 50, radius: 8, color: '#00ff00', type: '空心圆点' },
    { x: 50, y: 150, radius: 3, color: '#0000ff', type: '小点' },
    { x: 150, y: 150, radius: 10, color: '#ffff00', type: '大点' },
    { x: 100, y: 100, radius: 6, color: '#ff00ff', type: '中心点' }
  ]
  
  points.forEach((point, index) => {
    ctx.beginPath()
    
    if (index === 1) {
      // 空心圆点
      ctx.strokeStyle = point.color
      ctx.lineWidth = 2
      ctx.arc(point.x, point.y, point.radius, 0, Math.PI * 2)
      ctx.stroke()
    } else {
      // 实心圆点
      ctx.fillStyle = point.color
      ctx.arc(point.x, point.y, point.radius, 0, Math.PI * 2)
      ctx.fill()
    }
    
    // 添加发光效果
    ctx.shadowColor = point.color
    ctx.shadowBlur = 10
    ctx.beginPath()
    ctx.fillStyle = point.color
    ctx.arc(point.x, point.y, point.radius * 0.5, 0, Math.PI * 2)
    ctx.fill()
    ctx.shadowBlur = 0
  })
}

// 绘制线
const drawLines = () => {
  const canvas = lineCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  clearCanvas('line')
  
  // 直线
  ctx.beginPath()
  ctx.strokeStyle = '#ff0000'
  ctx.lineWidth = 2
  ctx.moveTo(20, 30)
  ctx.lineTo(180, 30)
  ctx.stroke()
  
  // 虚线
  ctx.beginPath()
  ctx.strokeStyle = '#00ff00'
  ctx.lineWidth = 2
  ctx.setLineDash([5, 5])
  ctx.moveTo(20, 60)
  ctx.lineTo(180, 60)
  ctx.stroke()
  ctx.setLineDash([]) // 重置虚线
  
  // 曲线
  ctx.beginPath()
  ctx.strokeStyle = '#0000ff'
  ctx.lineWidth = 3
  ctx.moveTo(20, 90)
  ctx.quadraticCurveTo(100, 120, 180, 90)
  ctx.stroke()
  
  // 贝塞尔曲线
  ctx.beginPath()
  ctx.strokeStyle = '#ffff00'
  ctx.lineWidth = 2
  ctx.moveTo(20, 130)
  ctx.bezierCurveTo(60, 110, 140, 150, 180, 130)
  ctx.stroke()
  
  // 折线
  ctx.beginPath()
  ctx.strokeStyle = '#ff00ff'
  ctx.lineWidth = 2
  ctx.moveTo(20, 170)
  ctx.lineTo(60, 160)
  ctx.lineTo(100, 180)
  ctx.lineTo(140, 160)
  ctx.lineTo(180, 170)
  ctx.stroke()
}

// 绘制图形
const drawShapes = () => {
  const canvas = shapeCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  clearCanvas('shape')
  
  // 矩形
  ctx.fillStyle = '#ff0000'
  ctx.fillRect(20, 20, 60, 40)
  
  // 圆形
  ctx.beginPath()
  ctx.fillStyle = '#00ff00'
  ctx.arc(150, 40, 25, 0, Math.PI * 2)
  ctx.fill()
  
  // 三角形
  ctx.beginPath()
  ctx.fillStyle = '#0000ff'
  ctx.moveTo(50, 100)
  ctx.lineTo(30, 140)
  ctx.lineTo(70, 140)
  ctx.closePath()
  ctx.fill()
  
  // 星形
  ctx.beginPath()
  ctx.fillStyle = '#ffff00'
  const centerX = 150, centerY = 120
  const spikes = 5
  const outerRadius = 25
  const innerRadius = 12
  
  for (let i = 0; i < spikes * 2; i++) {
    const radius = i % 2 === 0 ? outerRadius : innerRadius
    const angle = (i * Math.PI) / spikes
    const x = centerX + Math.cos(angle) * radius
    const y = centerY + Math.sin(angle) * radius
    
    if (i === 0) {
      ctx.moveTo(x, y)
    } else {
      ctx.lineTo(x, y)
    }
  }
  ctx.closePath()
  ctx.fill()
  
  // 渐变圆形
  const gradient = ctx.createRadialGradient(100, 170, 0, 100, 170, 20)
  gradient.addColorStop(0, '#ff00ff')
  gradient.addColorStop(1, '#00ffff')
  ctx.beginPath()
  ctx.fillStyle = gradient
  ctx.arc(100, 170, 20, 0, Math.PI * 2)
  ctx.fill()
}

// 绘制文字
const drawTexts = () => {
  const canvas = textCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  clearCanvas('text')
  
  // 基础文字
  ctx.fillStyle = '#ffffff'
  ctx.font = '16px Arial'
  ctx.fillText('Hello Canvas!', 20, 30)
  
  // 不同字体大小
  ctx.fillStyle = '#ff0000'
  ctx.font = 'bold 20px Arial'
  ctx.fillText('大字体', 20, 60)
  
  ctx.fillStyle = '#00ff00'
  ctx.font = '12px Arial'
  ctx.fillText('小字体', 20, 80)
  
  // 描边文字
  ctx.strokeStyle = '#0000ff'
  ctx.lineWidth = 1
  ctx.font = '18px Arial'
  ctx.strokeText('描边文字', 20, 110)
  
  // 居中文字
  ctx.fillStyle = '#ffff00'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('居中文字', 100, 140)
  
  // 旋转文字
  ctx.save()
  ctx.translate(100, 170)
  ctx.rotate(Math.PI / 6)
  ctx.fillStyle = '#ff00ff'
  ctx.font = '14px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('旋转文字', 0, 0)
  ctx.restore()
  
  // 重置文字对齐
  ctx.textAlign = 'left'
}

// 呼吸灯动画
const drawBreathingLights = () => {
  const canvas = breathingCanvas.value
  if (!canvas) return

  const ctx = canvas.getContext('2d')
  clearCanvas('breathing')

  const time = breathingTime.value

  // 创建多个呼吸灯效果 - 适配200x200画布
  const lights = [
    { x: 60, y: 60, color: '#ff0000', phase: 0, speed: 1 },
    { x: 140, y: 60, color: '#00ff00', phase: Math.PI / 3, speed: 1.2 },
    { x: 60, y: 140, color: '#0000ff', phase: Math.PI * 2 / 3, speed: 0.8 },
    { x: 140, y: 140, color: '#ffff00', phase: Math.PI, speed: 1.5 },
    { x: 100, y: 100, color: '#ff00ff', phase: Math.PI * 4 / 3, speed: 0.9 }
  ]

  lights.forEach(light => {
    // 计算呼吸效果 - 使用正弦波创建平滑的呼吸效果
    const breathingIntensity = (Math.sin(time * light.speed + light.phase) + 1) / 2

    // 基础参数 - 适配小画布
    const baseRadius = 8
    const maxRadius = 15
    const radius = baseRadius + (maxRadius - baseRadius) * breathingIntensity

    // 透明度变化
    const baseAlpha = 0.3
    const maxAlpha = 1.0
    const alpha = baseAlpha + (maxAlpha - baseAlpha) * breathingIntensity

    // 发光强度 - 适配小画布
    const glowRadius = radius * 1.5
    const glowIntensity = breathingIntensity * 15

    // 绘制外层发光效果
    const gradient = ctx.createRadialGradient(
      light.x, light.y, 0,
      light.x, light.y, glowRadius
    )

    // 解析颜色并添加透明度
    const r = parseInt(light.color.slice(1, 3), 16)
    const g = parseInt(light.color.slice(3, 5), 16)
    const b = parseInt(light.color.slice(5, 7), 16)

    gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, ${alpha})`)
    gradient.addColorStop(0.5, `rgba(${r}, ${g}, ${b}, ${alpha * 0.5})`)
    gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0)`)

    ctx.beginPath()
    ctx.fillStyle = gradient
    ctx.arc(light.x, light.y, glowRadius, 0, Math.PI * 2)
    ctx.fill()

    // 绘制核心光点
    ctx.beginPath()
    ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${alpha})`
    ctx.shadowColor = light.color
    ctx.shadowBlur = glowIntensity
    ctx.arc(light.x, light.y, radius, 0, Math.PI * 2)
    ctx.fill()

    // 绘制内核高亮
    const innerGradient = ctx.createRadialGradient(
      light.x - radius * 0.3, light.y - radius * 0.3, 0,
      light.x, light.y, radius * 0.8
    )
    innerGradient.addColorStop(0, `rgba(255, 255, 255, ${alpha * 0.8})`)
    innerGradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, ${alpha})`)

    ctx.beginPath()
    ctx.fillStyle = innerGradient
    ctx.arc(light.x, light.y, radius * 0.8, 0, Math.PI * 2)
    ctx.fill()

    // 重置阴影
    ctx.shadowBlur = 0
  })

  // 绘制连接线（可选的装饰效果）
  if (lights.length > 1) {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)'
    ctx.lineWidth = 1
    ctx.setLineDash([5, 5])

    for (let i = 0; i < lights.length; i++) {
      for (let j = i + 1; j < lights.length; j++) {
        const alpha = (Math.sin(time * 0.5 + i + j) + 1) / 2 * 0.3
        ctx.strokeStyle = `rgba(255, 255, 255, ${alpha})`

        ctx.beginPath()
        ctx.moveTo(lights[i].x, lights[i].y)
        ctx.lineTo(lights[j].x, lights[j].y)
        ctx.stroke()
      }
    }

    ctx.setLineDash([])
  }
}

// 开始呼吸灯动画
const startBreathingAnimation = () => {
  if (isBreathingActive.value) return

  isBreathingActive.value = true
  breathingTime.value = 0

  const animate = () => {
    if (!isBreathingActive.value) return

    breathingTime.value += 0.05
    drawBreathingLights()
    breathingAnimationId.value = requestAnimationFrame(animate)
  }

  animate()
}

// 停止呼吸灯动画
const stopBreathingAnimation = () => {
  isBreathingActive.value = false
  if (breathingAnimationId.value) {
    cancelAnimationFrame(breathingAnimationId.value)
    breathingAnimationId.value = null
  }
}

// 绘制综合示例
const drawCombined = () => {
  const canvas = combinedCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  clearCanvas('combined')
  
  // 绘制坐标系
  ctx.strokeStyle = '#333'
  ctx.lineWidth = 1
  
  // 网格
  for (let x = 0; x <= 600; x += 50) {
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, 300)
    ctx.stroke()
  }
  
  for (let y = 0; y <= 300; y += 50) {
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(600, y)
    ctx.stroke()
  }
  
  // 绘制一个简单的图表
  const data = [50, 120, 80, 150, 90, 180, 110]
  const barWidth = 60
  const maxHeight = 200
  
  data.forEach((value, index) => {
    const x = 50 + index * 80
    const height = (value / 200) * maxHeight
    const y = 250 - height
    
    // 绘制柱状图
    const gradient = ctx.createLinearGradient(0, y, 0, 250)
    gradient.addColorStop(0, '#00ffff')
    gradient.addColorStop(1, '#0066cc')
    
    ctx.fillStyle = gradient
    ctx.fillRect(x, y, barWidth, height)
    
    // 绘制数值
    ctx.fillStyle = '#ffffff'
    ctx.font = '12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(value.toString(), x + barWidth / 2, y - 5)
    
    // 绘制标签
    ctx.fillText(`项目${index + 1}`, x + barWidth / 2, 270)
  })
  
  // 标题
  ctx.fillStyle = '#ffffff'
  ctx.font = 'bold 20px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('数据可视化示例', 300, 30)
  
  ctx.textAlign = 'left'
}

// 动画效果
const animateCombined = () => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
  
  const animate = () => {
    animationTime.value += 0.05
    
    const canvas = combinedCanvas.value
    if (!canvas) return
    
    const ctx = canvas.getContext('2d')
    clearCanvas('combined')
    
    // 动态波浪线
    ctx.beginPath()
    ctx.strokeStyle = '#00ffff'
    ctx.lineWidth = 3
    
    for (let x = 0; x <= 600; x += 2) {
      const y = 150 + Math.sin((x + animationTime.value * 50) * 0.02) * 30
      if (x === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }
    }
    ctx.stroke()
    
    // 旋转的星星
    for (let i = 0; i < 5; i++) {
      ctx.save()
      const x = 100 + i * 100
      const y = 80
      ctx.translate(x, y)
      ctx.rotate(animationTime.value + i * 0.5)
      
      // 绘制星星
      ctx.beginPath()
      ctx.fillStyle = `hsl(${(animationTime.value * 50 + i * 60) % 360}, 100%, 50%)`
      
      for (let j = 0; j < 5 * 2; j++) {
        const radius = j % 2 === 0 ? 15 : 7
        const angle = (j * Math.PI) / 5
        const px = Math.cos(angle) * radius
        const py = Math.sin(angle) * radius
        
        if (j === 0) {
          ctx.moveTo(px, py)
        } else {
          ctx.lineTo(px, py)
        }
      }
      ctx.closePath()
      ctx.fill()
      ctx.restore()
    }
    
    // 跳动的圆点
    for (let i = 0; i < 10; i++) {
      const x = 50 + i * 50
      const y = 220 + Math.sin(animationTime.value * 2 + i * 0.5) * 20
      
      ctx.beginPath()
      ctx.fillStyle = `hsl(${(i * 36) % 360}, 100%, 50%)`
      ctx.arc(x, y, 8, 0, Math.PI * 2)
      ctx.fill()
    }
    
    animationId.value = requestAnimationFrame(animate)
  }
  
  animate()
}

// 初始化所有画布
const initCanvases = () => {
  // 为所有画布设置黑色背景
  const canvases = [pointCanvas, lineCanvas, shapeCanvas, textCanvas, breathingCanvas, combinedCanvas]
  canvases.forEach(canvasRef => {
    if (canvasRef.value) {
      const ctx = canvasRef.value.getContext('2d')
      ctx.fillStyle = '#000'
      ctx.fillRect(0, 0, canvasRef.value.width, canvasRef.value.height)
    }
  })

  // 绘制初始内容
  drawPoints()
  drawLines()
  drawShapes()
  drawTexts()
  clearCanvas('breathing') // 呼吸灯初始为空，需要手动启动
  drawCombined()
}

onMounted(() => {
  initCanvases()
})
</script>

<style scoped>
.canvas-basics {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.canvas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 15px;
  margin: 20px 0;
  max-width: 1200px;
}

.canvas-item {
  background: #222;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.canvas-item h3 {
  margin: 0 0 10px 0;
  color: #fff;
  font-size: 16px;
}

.canvas-item canvas {
  border: 2px solid #444;
  border-radius: 4px;
  background: #000;
}

.controls {
  margin-top: 10px;
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.controls button {
  padding: 5px 10px;
  background: #0066cc;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.controls button:hover {
  background: #0088ff;
}

.breathing-lights {
  border: 2px solid #444;
  box-shadow: 0 0 20px rgba(255, 0, 255, 0.3);
  transition: box-shadow 0.3s ease;
}

.breathing-lights:hover {
  box-shadow: 0 0 30px rgba(255, 0, 255, 0.5);
}

.combined-example {
  margin-top: 30px;
  background: #222;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.combined-example h3 {
  margin: 0 0 15px 0;
  color: #fff;
}

.combined-example canvas {
  border: 2px solid #444;
  border-radius: 4px;
  background: #000;
}

.combined-example .controls {
  margin-top: 15px;
}
</style>
